#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Monthly-Level Weather-Electricity Correlation Analysis (Streamlined)
===================================================================

This script performs streamlined correlation analysis between monthly weather data
and monthly electricity consumption data with adaptive data parsing capabilities.

Key Features:
- Adaptive column detection for weather data files
- Robust parsing for different weather data formats
- Raw weather data focus (no feature engineering)
- Automatic weather feature identification
- Backward compatibility with existing data files
- Simple correlation analysis (Pearson & Spearman)
- Statistical significance testing (p-values)
- Single essential heatmap visualization
- Minimal output files (correlation results + heatmap)
- Console-based summary reporting

Author: Augment Agent
Date: 2025-07-25
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
import os
from datetime import datetime
import warnings
import re
from typing import List, Dict, Tuple, Optional, Union

warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class AdaptiveWeatherDataParser:
    """
    Adaptive parser for weather data files with different formats and column structures
    """

    def __init__(self):
        """Initialize the adaptive parser"""
        # Common weather-related keywords for column detection
        self.weather_keywords = {
            'temperature': ['温', '温度', 'temp', '气温', '最高温', '最低温', '平均温', '高温', '低温'],
            'precipitation': ['降水', '雨', '雨量', '降雨', 'rain', 'precipitation', '降水量'],
            'humidity': ['湿度', 'humidity', '相对湿度', '湿'],
            'wind': ['风', 'wind', '风速', '风力', '风向'],
            'pressure': ['压', 'pressure', '气压', '大气压'],
            'weather_condition': ['天气', 'weather', '天气状况', '天气情况'],
            'sunshine': ['日照', 'sunshine', '光照', '阳光'],
            'visibility': ['能见度', 'visibility'],
            'aqi': ['aqi', 'AQI', '空气质量', '污染'],
            'cloud': ['云', 'cloud', '云量', '多云', '晴'],
            'days': ['天数', 'days', '日数']
        }

        # Date-related column patterns
        self.date_patterns = [
            r'年月', r'日期', r'date', r'time', r'年份', r'月份', r'年', r'月'
        ]

        # Metadata columns to exclude from analysis
        self.metadata_patterns = [
            r'天数', r'月天数', r'days', r'count', r'序号', r'index', r'id'
        ]

    def detect_file_format(self, file_path: str) -> str:
        """
        Detect the file format based on extension

        Args:
            file_path: Path to the data file

        Returns:
            File format ('csv' or 'excel')
        """
        if file_path.lower().endswith('.csv'):
            return 'csv'
        elif file_path.lower().endswith(('.xlsx', '.xls')):
            return 'excel'
        else:
            raise ValueError(f"Unsupported file format: {file_path}")

    def load_weather_data(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        Load weather data from file with adaptive format detection

        Args:
            file_path: Path to the weather data file
            sheet_name: Sheet name for Excel files (optional)

        Returns:
            Loaded DataFrame
        """
        file_format = self.detect_file_format(file_path)

        try:
            if file_format == 'csv':
                df = pd.read_csv(file_path, encoding='utf-8')
            else:  # excel
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(file_path)

            print(f"Successfully loaded weather data from {file_path}")
            print(f"  Format: {file_format}")
            print(f"  Shape: {df.shape}")
            print(f"  Columns: {list(df.columns)}")

            return df

        except Exception as e:
            print(f"Error loading weather data from {file_path}: {str(e)}")
            raise

    def is_weather_column(self, column_name: str) -> bool:
        """
        Determine if a column is weather-related based on keywords (raw data only)

        Args:
            column_name: Name of the column to check

        Returns:
            True if column appears to be weather-related (raw data)
        """
        column_lower = column_name.lower()

        # Check against weather keywords for raw data only
        for category, keywords in self.weather_keywords.items():
            for keyword in keywords:
                if keyword.lower() in column_lower:
                    return True

        return False

    def is_date_column(self, column_name: str) -> bool:
        """
        Determine if a column is date-related

        Args:
            column_name: Name of the column to check

        Returns:
            True if column appears to be date-related
        """
        column_lower = column_name.lower()

        for pattern in self.date_patterns:
            if re.search(pattern, column_lower):
                return True

        return False

    def is_metadata_column(self, column_name: str) -> bool:
        """
        Determine if a column is metadata (should be excluded from analysis)

        Args:
            column_name: Name of the column to check

        Returns:
            True if column appears to be metadata
        """
        column_lower = column_name.lower()

        for pattern in self.metadata_patterns:
            if re.search(pattern, column_lower):
                return True

        return False


class MonthlyWeatherElectricityCorrelation:
    """Enhanced monthly-level weather-electricity correlation analysis with adaptive data parsing"""

    def __init__(self,
                 electricity_data_path='data/龙泉代理购电.xlsx',
                 monthly_weather_path='data/丽水市_月度统计_201901_202508.csv'):
        """
        Initialize the correlation analyzer with adaptive parsing capabilities

        Args:
            electricity_data_path: Path to the electricity data file (contains monthly data sheet)
            monthly_weather_path: Path to the monthly weather data file (supports CSV and Excel)
        """
        self.electricity_data_path = electricity_data_path
        self.monthly_weather_path = monthly_weather_path
        self.monthly_weather_df = None
        self.monthly_electricity_df = None
        self.merged_monthly_df = None
        self.weather_columns = []
        self.electricity_columns = []  # Will be auto-detected
        self.correlation_results = {}
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Initialize adaptive parser
        self.weather_parser = AdaptiveWeatherDataParser()
        self.detected_weather_columns = []
        self.date_column_mapping = {}
        self.data_format_info = {}

    def load_and_prepare_data(self):
        """Load and prepare monthly data with adaptive parsing"""
        print(f"Loading monthly data with adaptive parsing...")
        print(f"  Electricity data: {self.electricity_data_path}")
        print(f"  Monthly weather data: {self.monthly_weather_path}")

        # Load monthly electricity data directly from the '月数据' sheet
        self.monthly_electricity_df = pd.read_excel(self.electricity_data_path, sheet_name='月数据')

        # Load monthly weather data using adaptive parser
        self.monthly_weather_df = self.weather_parser.load_weather_data(self.monthly_weather_path)

        # Analyze and prepare weather data structure
        self.analyze_weather_data_structure()

        # Prepare monthly electricity data
        self.prepare_monthly_electricity_data()

        # Merge monthly weather and electricity data
        self.merge_monthly_data()

        print(f"Data preparation completed:")
        print(f"  - Monthly weather records: {len(self.monthly_weather_df)}")
        print(f"  - Monthly electricity records: {len(self.monthly_electricity_df)}")
        print(f"  - Merged monthly records: {len(self.merged_monthly_df)}")
        print(f"  - Detected weather columns: {len(self.detected_weather_columns)}")

        return self.merged_monthly_df

    def analyze_weather_data_structure(self):
        """
        Analyze the structure of weather data and identify relevant columns
        """
        print("Analyzing weather data structure...")

        # Store original column information
        all_columns = list(self.monthly_weather_df.columns)
        self.data_format_info['original_columns'] = all_columns
        self.data_format_info['total_columns'] = len(all_columns)

        # Categorize columns (focus on raw numeric weather data only)
        date_columns = []
        weather_columns = []
        metadata_columns = []
        other_columns = []

        for col in all_columns:
            if self.weather_parser.is_date_column(col):
                date_columns.append(col)
            elif self.weather_parser.is_metadata_column(col):
                metadata_columns.append(col)
            elif self.weather_parser.is_weather_column(col):
                # Only include numeric weather columns for correlation analysis
                if pd.api.types.is_numeric_dtype(self.monthly_weather_df[col]):
                    weather_columns.append(col)
                else:
                    other_columns.append(col)
            else:
                # For unknown columns, only include if numeric and potentially weather-related
                if pd.api.types.is_numeric_dtype(self.monthly_weather_df[col]):
                    # Additional check: exclude obvious non-weather numeric columns
                    if not any(exclude_word in col.lower() for exclude_word in ['id', 'index', '序号', 'count']):
                        weather_columns.append(col)
                    else:
                        other_columns.append(col)
                else:
                    other_columns.append(col)

        # Store categorized columns
        self.data_format_info['date_columns'] = date_columns
        self.data_format_info['weather_columns'] = weather_columns
        self.data_format_info['metadata_columns'] = metadata_columns
        self.data_format_info['other_columns'] = other_columns

        # Set detected weather columns for analysis
        self.detected_weather_columns = weather_columns

        # Identify date column for merging
        self.identify_date_column_mapping()

        print(f"Weather data structure analysis completed:")
        print(f"  - Date columns: {date_columns}")
        print(f"  - Weather columns ({len(weather_columns)}): {weather_columns}")
        print(f"  - Metadata columns: {metadata_columns}")
        print(f"  - Other columns: {other_columns}")

        return self.data_format_info

    def identify_date_column_mapping(self):
        """
        Identify and map date columns for merging operations
        """
        print("Identifying date column mapping...")

        date_columns = self.data_format_info['date_columns']

        # Priority order for date column selection
        date_column_priorities = ['年月', '日期', 'date', '年份_月份']

        # Find the best date column
        primary_date_col = None
        for priority_col in date_column_priorities:
            if priority_col in date_columns:
                primary_date_col = priority_col
                break

        if not primary_date_col and date_columns:
            primary_date_col = date_columns[0]  # Use first available date column

        # Handle different date formats
        if primary_date_col:
            self.date_column_mapping['primary'] = primary_date_col

            # Check if we need to create a standardized date column
            sample_value = str(self.monthly_weather_df[primary_date_col].iloc[0])

            if '-' in sample_value and len(sample_value.split('-')) == 2:
                # Already in YYYY-MM format
                self.date_column_mapping['format'] = 'YYYY-MM'
                self.date_column_mapping['needs_conversion'] = False
            elif '年' in sample_value and '月' in sample_value:
                # Chinese format like "2019年1月"
                self.date_column_mapping['format'] = 'Chinese'
                self.date_column_mapping['needs_conversion'] = True
            else:
                # Try to detect other formats
                self.date_column_mapping['format'] = 'Unknown'
                self.date_column_mapping['needs_conversion'] = True

        # Check for separate year and month columns
        if '年' in self.monthly_weather_df.columns and '月' in self.monthly_weather_df.columns:
            self.date_column_mapping['has_separate_year_month'] = True
            self.date_column_mapping['year_col'] = '年'
            self.date_column_mapping['month_col'] = '月'
        elif '年份' in self.monthly_weather_df.columns and '月份' in self.monthly_weather_df.columns:
            self.date_column_mapping['has_separate_year_month'] = True
            self.date_column_mapping['year_col'] = '年份'
            self.date_column_mapping['month_col'] = '月份'
        else:
            self.date_column_mapping['has_separate_year_month'] = False

        print(f"Date column mapping: {self.date_column_mapping}")

        return self.date_column_mapping

    def prepare_monthly_electricity_data(self):
        """Prepare monthly electricity data for analysis with auto-detection of electricity columns"""
        print("Preparing monthly electricity data...")

        # Convert month format from YYYYMM to YYYY-MM for consistency
        self.monthly_electricity_df['年月'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.strftime('%Y-%m')

        # Extract year and month for additional analysis if needed
        self.monthly_electricity_df['年'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.year
        self.monthly_electricity_df['月'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.month

        # Auto-detect electricity columns (exclude date-related columns)
        self.detect_electricity_columns()

        print(f"Monthly electricity data preparation completed:")
        print(f"  Date range: {self.monthly_electricity_df['年月'].min()} to {self.monthly_electricity_df['年月'].max()}")
        print(f"  Records: {len(self.monthly_electricity_df)}")
        print(f"  Columns: {list(self.monthly_electricity_df.columns)}")
        print(f"  Detected electricity columns: {self.electricity_columns}")

        return self.monthly_electricity_df

    def detect_electricity_columns(self):
        """
        Auto-detect electricity-related columns in the electricity data
        """
        print("Auto-detecting electricity columns...")

        # Exclude date-related and metadata columns
        exclude_columns = ['月份', '年月', '年', '月']

        # Common electricity-related keywords
        electricity_keywords = [
            '居民', '农业', '代理购电', '电网代购用户', '工业', '商业',
            '用电', '电量', '电力', '购电', '代购', '用户'
        ]

        detected_columns = []

        for col in self.monthly_electricity_df.columns:
            if col in exclude_columns:
                continue

            # Check if column is numeric (electricity data should be numeric)
            if pd.api.types.is_numeric_dtype(self.monthly_electricity_df[col]):
                # Check if column name contains electricity-related keywords
                is_electricity_col = any(keyword in col for keyword in electricity_keywords)

                if is_electricity_col or col not in exclude_columns:
                    # Additional check: ensure the column has meaningful data
                    non_zero_count = (self.monthly_electricity_df[col] > 0).sum()
                    if non_zero_count > 0:  # At least some non-zero values
                        detected_columns.append(col)

        # If no specific electricity columns found, use all numeric columns except excluded ones
        if not detected_columns:
            for col in self.monthly_electricity_df.columns:
                if (col not in exclude_columns and
                    pd.api.types.is_numeric_dtype(self.monthly_electricity_df[col])):
                    detected_columns.append(col)

        self.electricity_columns = detected_columns

        print(f"Detected electricity columns: {self.electricity_columns}")

        return self.electricity_columns
    
    def merge_monthly_data(self):
        """Merge monthly weather and electricity data using adaptive column mapping"""
        print("Merging monthly weather and electricity data with adaptive mapping...")

        # Prepare monthly weather data for merging
        weather_df = self.monthly_weather_df.copy()

        # Create standardized date column for merging
        weather_df = self.create_standardized_date_column(weather_df)

        # Use detected weather columns instead of hardcoded exclusions
        self.weather_columns = self.detected_weather_columns.copy()

        print(f"Weather columns to analyze ({len(self.weather_columns)}): {self.weather_columns}")

        # Merge on year-month
        self.merged_monthly_df = pd.merge(
            self.monthly_electricity_df,
            weather_df,
            left_on='年月',
            right_on='年月_标准',
            how='inner'
        )

        print(f"Monthly data merge completed:")
        print(f"  Weather variables: {len(self.weather_columns)}")
        print(f"  Electricity categories: {len(self.electricity_columns)}")
        print(f"  Merged records: {len(self.merged_monthly_df)}")
        print(f"  Merged columns: {list(self.merged_monthly_df.columns)}")

        # Check which date column is available after merge
        date_col = None
        for col in ['年月', '年月_标准', '年月_x', '年月_y']:
            if col in self.merged_monthly_df.columns:
                date_col = col
                break

        if date_col:
            print(f"  Date range: {self.merged_monthly_df[date_col].min()} to {self.merged_monthly_df[date_col].max()}")
        else:
            print("  Warning: No date column found after merge")

        return self.merged_monthly_df

    def create_standardized_date_column(self, weather_df: pd.DataFrame) -> pd.DataFrame:
        """
        Create a standardized date column for merging operations

        Args:
            weather_df: Weather DataFrame to process

        Returns:
            DataFrame with standardized date column
        """
        print("Creating standardized date column...")

        mapping = self.date_column_mapping

        if mapping.get('has_separate_year_month', False):
            # Create from separate year and month columns
            year_col = mapping['year_col']
            month_col = mapping['month_col']

            weather_df['年月_标准'] = (
                weather_df[year_col].astype(str) + '-' +
                weather_df[month_col].astype(str).str.zfill(2)
            )
            print(f"Created standardized date from {year_col} and {month_col}")

        elif mapping.get('primary'):
            primary_col = mapping['primary']

            if not mapping.get('needs_conversion', True):
                # Already in correct format
                weather_df['年月_标准'] = weather_df[primary_col].astype(str)
                print(f"Used existing date column {primary_col}")
            else:
                # Need to convert format
                if mapping.get('format') == 'Chinese':
                    # Convert Chinese format like "2019年1月" to "2019-01"
                    weather_df['年月_标准'] = weather_df[primary_col].astype(str).apply(
                        self.convert_chinese_date_format
                    )
                    print(f"Converted Chinese date format from {primary_col}")
                else:
                    # Try generic conversion
                    try:
                        weather_df['年月_标准'] = pd.to_datetime(weather_df[primary_col]).dt.strftime('%Y-%m')
                        print(f"Converted date format from {primary_col}")
                    except:
                        # Fallback: use as-is and hope for the best
                        weather_df['年月_标准'] = weather_df[primary_col].astype(str)
                        print(f"Used {primary_col} as-is (conversion failed)")
        else:
            raise ValueError("No suitable date column found for merging")

        return weather_df

    def convert_chinese_date_format(self, date_str: str) -> str:
        """
        Convert Chinese date format to standard YYYY-MM format

        Args:
            date_str: Date string in Chinese format

        Returns:
            Standardized date string
        """
        try:
            # Handle formats like "2019年1月", "2019年01月"
            if '年' in date_str and '月' in date_str:
                parts = date_str.replace('年', '-').replace('月', '').split('-')
                if len(parts) >= 2:
                    year = parts[0]
                    month = parts[1].zfill(2)
                    return f"{year}-{month}"

            # Fallback: return as-is
            return date_str

        except Exception:
            return date_str
    
    def calculate_correlations(self):
        """Calculate Pearson and Spearman correlations with p-values"""
        print("\nCalculating monthly correlations...")
        
        results = []
        
        for elec_col in self.electricity_columns:
            for weather_col in self.weather_columns:
                # Get clean data (remove NaN values)
                elec_data = self.merged_monthly_df[elec_col].dropna()
                weather_data = self.merged_monthly_df[weather_col].dropna()
                
                # Find common indices
                common_idx = elec_data.index.intersection(weather_data.index)
                if len(common_idx) < 5:  # Need at least 5 data points for monthly data
                    continue
                
                elec_clean = elec_data.loc[common_idx]
                weather_clean = weather_data.loc[common_idx]
                
                # Calculate Pearson correlation
                try:
                    pearson_corr, pearson_p = pearsonr(weather_clean, elec_clean)
                except:
                    pearson_corr, pearson_p = np.nan, np.nan
                
                # Calculate Spearman correlation
                try:
                    spearman_corr, spearman_p = spearmanr(weather_clean, elec_clean)
                except:
                    spearman_corr, spearman_p = np.nan, np.nan
                
                # Store results
                result = {
                    'electricity_type': elec_col,
                    'weather_variable': weather_col,
                    'pearson_correlation': pearson_corr,
                    'pearson_p_value': pearson_p,
                    'spearman_correlation': spearman_corr,
                    'spearman_p_value': spearman_p,
                    'sample_size': len(common_idx),
                    'pearson_significant': pearson_p < 0.05 if not np.isnan(pearson_p) else False,
                    'spearman_significant': spearman_p < 0.05 if not np.isnan(spearman_p) else False,
                    'pearson_abs_corr': abs(pearson_corr) if not np.isnan(pearson_corr) else 0,
                    'spearman_abs_corr': abs(spearman_corr) if not np.isnan(spearman_corr) else 0
                }
                
                results.append(result)
        
        self.correlation_results = pd.DataFrame(results)
        print(f"Calculated correlations for {len(results)} weather-electricity pairs")
        
        return self.correlation_results
    
    def filter_significant_correlations(self, min_correlation=0.3, significance_level=0.05):
        """Filter for statistically significant high correlations"""
        print(f"\nFiltering for significant correlations (|r| >= {min_correlation}, p < {significance_level})...")
        
        # Filter for Pearson correlations
        pearson_significant = self.correlation_results[
            (self.correlation_results['pearson_abs_corr'] >= min_correlation) &
            (self.correlation_results['pearson_p_value'] < significance_level)
        ].copy()
        
        # Filter for Spearman correlations
        spearman_significant = self.correlation_results[
            (self.correlation_results['spearman_abs_corr'] >= min_correlation) &
            (self.correlation_results['spearman_p_value'] < significance_level)
        ].copy()
        
        print(f"Found {len(pearson_significant)} significant Pearson correlations")
        print(f"Found {len(spearman_significant)} significant Spearman correlations")
        
        return pearson_significant, spearman_significant

    def create_correlation_heatmaps(self):
        """Create simplified correlation heatmap (single combined visualization)"""
        print("\nCreating correlation heatmap...")

        # Prepare data for heatmap (focus on Spearman correlations as they're more robust)
        spearman_data = []

        for _, row in self.correlation_results.iterrows():
            spearman_data.append({
                'Weather_Variable': row['weather_variable'],
                row['electricity_type']: row['spearman_correlation']
            })

        # Convert to DataFrame
        spearman_df = pd.DataFrame(spearman_data).groupby('Weather_Variable').first()

        # Create single heatmap
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # Spearman heatmap (more robust for non-linear relationships)
        sns.heatmap(spearman_df, annot=True, cmap='RdBu_r', center=0,
                   fmt='.3f', ax=ax, cbar_kws={'label': 'Spearman Correlation'})
        ax.set_title('Monthly Weather-Electricity Correlations (Spearman)')
        ax.set_xlabel('Electricity Types')
        ax.set_ylabel('Weather Variables')

        plt.tight_layout()

        # Save heatmap
        result_dir = os.path.join('feature_analysis_results', 'result')
        os.makedirs(result_dir, exist_ok=True)
        heatmap_filename = os.path.join(result_dir,f'monthly_correlation_heatmap_{self.timestamp}.png')
        plt.savefig(heatmap_filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Saved correlation heatmap: {heatmap_filename}")

        return heatmap_filename



    def save_results(self):
        """Save essential correlation results (minimal output)"""
        print("\nSaving correlation results...")

        # Save only complete results - single comprehensive file
        # Create result directory if it doesn't exist
        result_dir = os.path.join('feature_analysis_results', 'result')
        os.makedirs(result_dir, exist_ok=True)
        
        # Save to result directory
        complete_filename = os.path.join(result_dir, f'monthly_weather_electricity_correlations_{self.timestamp}.csv')
        self.correlation_results.to_csv(complete_filename, index=False, encoding='utf-8-sig')
        print(f"Saved correlation results: {complete_filename}")

        return complete_filename

    def print_summary_report(self):
        """Print summary report to console (no file output)"""
        print("\n" + "="*80)
        print("CORRELATION ANALYSIS SUMMARY")
        print("="*80)

        for elec_type in self.electricity_columns:
            elec_data = self.correlation_results[
                self.correlation_results['electricity_type'] == elec_type
            ]

            if len(elec_data) == 0:
                continue

            print(f"\n{elec_type} (Electricity Type):")
            print(f"  Total weather variables analyzed: {len(elec_data)}")

            # Pearson statistics
            pearson_corrs = elec_data['pearson_correlation'].dropna()
            pearson_sig_count = elec_data['pearson_significant'].sum()

            if len(pearson_corrs) > 0:
                print(f"  Pearson correlations:")
                print(f"    Mean |correlation|: {pearson_corrs.abs().mean():.3f}")
                print(f"    Max |correlation|: {pearson_corrs.abs().max():.3f}")
                print(f"    Significant correlations (p<0.05): {pearson_sig_count}")

            # Spearman statistics
            spearman_corrs = elec_data['spearman_correlation'].dropna()
            spearman_sig_count = elec_data['spearman_significant'].sum()

            if len(spearman_corrs) > 0:
                print(f"  Spearman correlations:")
                print(f"    Mean |correlation|: {spearman_corrs.abs().mean():.3f}")
                print(f"    Max |correlation|: {spearman_corrs.abs().max():.3f}")
                print(f"    Significant correlations (p<0.05): {spearman_sig_count}")

    def generate_summary_report(self):
        """Generate a summary report of the monthly correlation analysis"""
        print("\nGenerating monthly summary report...")

        # Calculate summary statistics
        summary_stats = {}

        for elec_type in self.electricity_columns:
            elec_data = self.correlation_results[
                self.correlation_results['electricity_type'] == elec_type
            ]

            if len(elec_data) == 0:
                continue

            # Pearson statistics
            pearson_corrs = elec_data['pearson_correlation'].dropna()
            pearson_sig_count = elec_data['pearson_significant'].sum()

            # Spearman statistics
            spearman_corrs = elec_data['spearman_correlation'].dropna()
            spearman_sig_count = elec_data['spearman_significant'].sum()

            summary_stats[elec_type] = {
                'total_weather_variables': len(elec_data),
                'pearson_mean_abs_corr': pearson_corrs.abs().mean(),
                'pearson_max_abs_corr': pearson_corrs.abs().max(),
                'pearson_significant_count': pearson_sig_count,
                'spearman_mean_abs_corr': spearman_corrs.abs().mean(),
                'spearman_max_abs_corr': spearman_corrs.abs().max(),
                'spearman_significant_count': spearman_sig_count
            }

        # Save summary report
        summary_df = pd.DataFrame(summary_stats).T
        summary_filename = f'monthly_correlation_summary_{self.timestamp}.csv'
        summary_df.to_csv(summary_filename, encoding='utf-8-sig')

        print(f"Saved summary report: {summary_filename}")

        # Print summary to console
        print("\n" + "="*80)
        print("MONTHLY WEATHER-ELECTRICITY CORRELATION ANALYSIS SUMMARY")
        print("="*80)

        for elec_type in self.electricity_columns:
            if elec_type in summary_stats:
                stats = summary_stats[elec_type]
                print(f"\n{elec_type} (Electricity Type):")
                print(f"  Total weather variables analyzed: {stats['total_weather_variables']}")
                print(f"  Pearson correlations:")
                print(f"    Mean |correlation|: {stats['pearson_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['pearson_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['pearson_significant_count']}")
                print(f"  Spearman correlations:")
                print(f"    Mean |correlation|: {stats['spearman_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['spearman_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['spearman_significant_count']}")

        return summary_filename

    def run_complete_analysis(self):
        """Run the streamlined monthly correlation analysis (raw data focus)"""
        print("="*80)
        print("MONTHLY WEATHER-ELECTRICITY CORRELATION ANALYSIS (RAW DATA)")
        print("="*80)

        # Load and prepare data
        self.load_and_prepare_data()

        # Calculate correlations
        self.calculate_correlations()

        # Create single visualization
        heatmap_file = self.create_correlation_heatmaps()

        # Save essential results only
        complete_file = self.save_results()

        # Generate console summary (no separate file)
        self.print_summary_report()

        print("\n" + "="*80)
        print("ANALYSIS COMPLETE!")
        print("="*80)
        print("Generated files:")
        print(f"  - Correlation results: {complete_file}")
        print(f"  - Correlation heatmap: {heatmap_file}")

        return {
            'complete_results': complete_file,
            'heatmap': heatmap_file,
            'timestamp': self.timestamp
        }


def main():
    """Main execution function with enhanced adaptive parsing"""
    try:
        # Initialize analyzer with adaptive parsing
        analyzer = MonthlyWeatherElectricityCorrelation(
            electricity_data_path='./data/居民、农业2019-.xlsx',
            monthly_weather_path='./data/丽水市_月度统计_201901_202508.csv'
        )

        # Run complete analysis
        results = analyzer.run_complete_analysis()

        print(f"\nMonthly correlation analysis completed successfully!")
        print(f"Results saved with timestamp: {results['timestamp']}")

        # Print summary of adaptive parsing results
        print("\nAdaptive Parsing Summary:")
        weather_format = analyzer.weather_parser.detect_file_format(
            analyzer.monthly_weather_path
        )
        print(f"  - Weather data format: {weather_format}")
        detected_count = len(analyzer.detected_weather_columns)
        print(f"  - Detected weather columns: {detected_count}")
        primary_date = analyzer.date_column_mapping.get(
            'primary', 'Auto-detected'
        )
        print(f"  - Date column mapping: {primary_date}")
        total_cols = analyzer.data_format_info.get('total_columns', 0)
        print(f"  - Data format info: {total_cols} total columns")

    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()


def test_adaptive_parsing(weather_file_path: str):
    """
    Test function to validate adaptive parsing with different weather data files

    Args:
        weather_file_path: Path to weather data file to test
    """
    print(f"Testing adaptive parsing with: {weather_file_path}")

    try:
        # Initialize parser
        parser = AdaptiveWeatherDataParser()

        # Test file format detection
        file_format = parser.detect_file_format(weather_file_path)
        print(f"Detected format: {file_format}")

        # Test data loading
        df = parser.load_weather_data(weather_file_path)
        print(f"Loaded data shape: {df.shape}")

        # Test column categorization
        weather_cols = []
        date_cols = []
        metadata_cols = []

        for col in df.columns:
            if parser.is_weather_column(col):
                weather_cols.append(col)
            elif parser.is_date_column(col):
                date_cols.append(col)
            elif parser.is_metadata_column(col):
                metadata_cols.append(col)

        print("Column categorization:")
        print(f"  - Weather columns: {weather_cols}")
        print(f"  - Date columns: {date_cols}")
        print(f"  - Metadata columns: {metadata_cols}")

        return True

    except Exception as e:
        print(f"Error testing adaptive parsing: {str(e)}")
        return False


if __name__ == "__main__":
    main()
